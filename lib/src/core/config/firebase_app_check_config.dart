import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter/foundation.dart';

class FirebaseAppCheckConfig {
  static Future<void> initialize() async {
    try {
      if (kIsWeb) {
        // Web configuration
        await FirebaseAppCheck.instance.activate(
          webProvider: ReCaptchaV3Provider('your-recaptcha-site-key'),
        );
      } else {
        // Check environment variable for production mode
        const env = String.fromEnvironment('ENV');
        const isProduction = env == 'prod';

        if (kDebugMode && !isProduction) {
          // For development - use debug provider
          await FirebaseAppCheck.instance.activate(
            androidProvider: AndroidProvider.debug,
            appleProvider: AppleProvider.debug,
          );

          // Disable auto-refresh in debug mode to prevent throttling
          await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(false);
          debugPrint('Firebase App Check initialized in DEBUG mode');
        } else {
          // For production - use Play Integrity and App Attest with throttling protection
          await FirebaseAppCheck.instance.activate(
            androidProvider: AndroidProvider.playIntegrity,
            appleProvider: AppleProvider.appAttest,
          );

          // Enable auto-refresh with careful throttling
          await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
          debugPrint('Firebase App Check initialized in PRODUCTION mode');
        }
      }
    } catch (e) {
      debugPrint('Failed to initialize Firebase App Check: $e');
      // Don't rethrow - allow app to continue without App Check if needed
    }
  }
  
  /// Get App Check token manually with error handling
  static Future<String?> getToken({bool forceRefresh = false}) async {
    try {
      final token = await FirebaseAppCheck.instance.getToken(forceRefresh);
      return token;
    } catch (e) {
      debugPrint('Failed to get App Check token: $e');
      return null;
    }
  }
  
  /// Check if App Check is properly configured
  static Future<bool> isConfigured() async {
    try {
      final token = await getToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
